from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import time
import json
import re
import undetected_chromedriver as uc

def get_iphone_page():
    """获取iPhone页面并保存HTML"""
    try:
        # 使用undetected_chromedriver来绕过检测
        options = uc.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-images')
        options.add_argument('--disable-javascript')
        
        # 初始化WebDriver
        driver = uc.Chrome(options=options)
        driver.set_page_load_timeout(30)
        
        # 访问页面
        url = "https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTM3CH/A"
        driver.get(url)
        
        # 等待页面加载
        time.sleep(3)
        
        # 保存页面源码
        with open('iphone_page.html', 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        print("HTML内容已保存到 iphone_page.html")
        
    except Exception as e:
        print(f"获取页面时发生错误: {e}")
    finally:
        try:
            driver.quit()
        except:
            pass

def extract_iphone_links_with_info_optimized(html_path):
    """优化的单线程版本，更安全高效"""
    with open(html_path, 'r', encoding='utf-8') as f:
        html = f.read()
    soup = BeautifulSoup(html, 'html.parser')

    # 优化的浏览器配置
    options = Options()
    options.add_argument('--headless')
    options.add_argument('--disable-gpu')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-images')
    options.add_argument('--disable-javascript')
    options.add_argument('--disable-plugins')
    options.add_argument('--disable-extensions')
    options.add_argument('--disable-logging')
    options.add_argument('--disable-default-apps')
    options.add_argument('--disable-sync')
    options.add_argument('--disable-translate')
    options.add_argument('--disable-web-security')
    options.add_argument('--disable-features=VizDisplayCompositor')
    
    driver = webdriver.Chrome(options=options)
    driver.set_page_load_timeout(10)

    # 先收集所有链接信息
    links_to_process = []
    for a in soup.find_all('a', href=True):
        href = a['href']
        match = re.match(r'https://www\.apple\.com\.cn/shop/buy-iphone/iphone-16-pro/([A-Z0-9]{7})/A', href)
        if match:
            sku = match.group(1)
            url = href
            capacity_tag = a.find('span', class_='dimensionCapacity')
            color_tag = a.find('span', class_='dimensionColor')
            capacity = ''
            color = ''
            if capacity_tag:
                capacity = capacity_tag.get_text(strip=True).replace('脚注²', '').replace('\u00a0脚注\u00a0²', '').replace('\xa0脚注\xa0²', '')
            if color_tag:
                color = color_tag.get_text(strip=True)
            links_to_process.append({
                'sku': sku,
                'url': url,
                'capacity': capacity,
                'color': color
            })

    results = []
    total_links = len(links_to_process)
    
    for i, link_info in enumerate(links_to_process, 1):
        print(f"处理进度: {i}/{total_links} - {link_info['sku']}")
        
        # 重试机制
        max_retries = 3
        for retry in range(max_retries):
            try:
                driver.get(link_info['url'])
                time.sleep(0.5)
                
                page_soup = BeautifulSoup(driver.page_source, 'html.parser')
                title = page_soup.title.string if page_soup.title else ''
                
                if 'Pro Max' in title:
                    model = 'iPhone 16 Pro Max'
                elif 'Pro' in title:
                    model = 'iPhone 16 Pro'
                else:
                    model = '未知'
                
                print(f"✓ 成功: {link_info['sku']} -> {model}")
                break
                
            except Exception as e:
                if retry < max_retries - 1:
                    print(f"⚠ 重试 {retry + 1}/{max_retries}: {link_info['sku']} - {e}")
                    time.sleep(1)
                else:
                    print(f"✗ 失败: {link_info['sku']} - {e}")
                    model = '未知'
        
        results.append({
            'sku': link_info['sku'],
            'url': link_info['url'],
            'capacity': link_info['capacity'],
            'color': link_info['color'],
            'model': model
        })
        
        # 在请求之间添加随机延迟
        if i < total_links:
            time.sleep(0.2 + (i % 3) * 0.1)
    
    driver.quit()
    return results

def main():
    # 首先获取页面
    print("正在获取iPhone页面...")
    get_iphone_page()
    
    # 然后提取链接信息
    print("正在提取链接信息...")
    html_path = 'iphone_page.html'
    results = extract_iphone_links_with_info_optimized(html_path)
    
    # 保存结果
    with open('iphone_sku_links.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f'已提取{len(results)}个SKU链接，结果已保存到iphone_sku_links.json')

if __name__ == '__main__':
    main()
